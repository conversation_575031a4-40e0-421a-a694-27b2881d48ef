import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

export enum CompType {
  CORE = 'สมรรถนะหลัก',
  FUNCTIONAL = 'ประจำสายงาน',
  MANAGERIAL = 'ทางการบริหาร',
}

export enum CareerType {
  ACADEMIC = 'วิชาการ',
  SUPPORT = 'สนับสนุน',
}

@Entity('competencies')
export class Competency {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'enum',
    enum: CompType,
  })
  comp_type: CompType;

  @Column({
    type: 'enum',
    enum: CareerType,
    nullable: true,
  })
  career_type: CareerType;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;
}
