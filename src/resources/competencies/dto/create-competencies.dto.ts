// import { IsEnum, IsOptional, IsString } from 'class-validator';
// import { Type } from 'class-transformer';
// import { CompType, CareerType } from '../entities/competencies.entity';

// export class CreateCompetenciesDto {
//   @IsEnum(CompType)
//   comp_type: CompType;

//   @IsEnum(CareerType)
//   @IsOptional()
//   career_type?: CareerType;

//   @IsString()
//   name: string;

//   @IsString()
//   @IsOptional()
//   description?: string;
// }

import { IsEnum, IsOptional, IsString } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { CompType, CareerType } from '../entities/competencies.entity';

export class CreateCompetenciesDto {
  @IsEnum(CompType)
  comp_type: CompType;

  @IsEnum(CareerType)
  @IsOptional()
  @Transform(({ value }) => (value === '' ? undefined : value))
  career_type?: CareerType;

  @IsString()
  name: string;

  @IsString()
  @IsOptional()
  description?: string;
}
