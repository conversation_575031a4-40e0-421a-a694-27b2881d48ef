import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import {
  Competency,
  CompType,
  CareerType,
} from './entities/competencies.entity';
import { CreateCompetenciesDto } from './dto/create-competencies.dto';
import { UpdateCompetenciesDto } from './dto/update-competencies.dto';
import type { DataParams, DataResponse } from 'src/types/params';

@Injectable()
export class CompetenciesService {
  constructor(
    @InjectRepository(Competency)
    private competencyRepository: Repository<Competency>,
  ) {}

  async create(createDto: CreateCompetenciesDto) {
    // Convert string enums if needed
    if (typeof createDto.comp_type === 'string') {
      createDto.comp_type = createDto.comp_type as CompType;
    }
    if (typeof createDto.career_type === 'string') {
      createDto.career_type = createDto.career_type as CareerType;
    }

    const competency = this.competencyRepository.create(createDto);
    return this.competencyRepository.save(competency);
  }

  //   async findAll(pag: DataParams): Promise<DataResponse<Competency>> {
  //     const [items, total] = await this.competencyRepository.findAndCount({
  //       where: {
  //         name: pag.search ? Like(`%${pag.search}%`) : undefined,
  //       },
  //       order: {
  //         [pag.sortBy || 'id']: pag.order || 'ASC',
  //       },
  //       skip: (pag.page - 1) * pag.limit,
  //       take: pag.limit,
  //       cache: true,
  //     });

  //     return {
  //       data: items,
  //       total,
  //       curPage: pag.page,
  //       hasNext: total > pag.page * pag.limit,
  //       hasPrev: pag.page > 1,
  //     };
  //   }

  async findAll(
    pag: DataParams,
    comp_type?: string,
    career_type?: string,
  ): Promise<DataResponse<Competency>> {
    const where: any = {};

    if (pag.search) {
      where.name = Like(`%${pag.search}%`);
    }

    if (comp_type) {
      where.comp_type = comp_type;
    }

    if (career_type) {
      where.career_type = career_type;
    }

    const [items, total] = await this.competencyRepository.findAndCount({
      where,
      order: {
        [pag.sortBy || 'id']: pag.order || 'ASC',
      },
      skip: (pag.page - 1) * pag.limit,
      take: pag.limit,
      cache: true,
    });

    return {
      data: items,
      total,
      curPage: pag.page,
      hasNext: total > pag.page * pag.limit,
      hasPrev: pag.page > 1,
    };
  }

  async findOne(id: number) {
    const competency = await this.competencyRepository.findOne({
      where: { id },
    });
    if (!competency) {
      throw new NotFoundException('Competency not found');
    }
    return competency;
  }

  async update(id: number, updateDto: UpdateCompetenciesDto) {
    const competency = await this.findOne(id);

    if (typeof updateDto.comp_type === 'string') {
      updateDto.comp_type = updateDto.comp_type as CompType;
    }
    if (typeof updateDto.career_type === 'string') {
      updateDto.career_type = updateDto.career_type as CareerType;
    }

    const updated = { ...competency, ...updateDto };
    return this.competencyRepository.save(updated);
  }

  async remove(id: number) {
    const competency = await this.findOne(id);
    return this.competencyRepository.remove(competency);
  }
}
