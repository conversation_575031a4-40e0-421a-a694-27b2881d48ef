import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseInterceptors,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import { CompetenciesService } from './competencies.service';
import { CreateCompetenciesDto } from './dto/create-competencies.dto';
import { UpdateCompetenciesDto } from './dto/update-competencies.dto';
import { ApiBearerAuth, ApiBody, ApiConsumes } from '@nestjs/swagger';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { DefaultQueryParams } from 'src/utils/default-query.decorator';
import type { DataParams } from 'src/types/params';
import { AuthGuard } from 'src/auth/auth.guard';
import { ApiQuery } from '@nestjs/swagger';

@ApiBearerAuth()
// @UseGuards(AuthGuard)
@Controller('competencies')
export class CompetenciesController {
  constructor(private readonly competenciesService: CompetenciesService) {}

  @Post()
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: 'object',
    schema: {
      type: 'object',
      properties: {
        comp_type: {
          type: 'string',
          enum: ['สมรรถนะหลัก', 'ประจำสายงาน', 'ทางการบริหาร'],
          example: 'สมรรถนะหลัก',
        },
        career_type: {
          type: 'string',
          enum: ['วิชาการ', 'สนับสนุน'],
          example: 'วิชาการ',
        },
        name: { type: 'string', example: 'การคิดเชิงวิเคราะห์' },
        description: {
          type: 'string',
          example: 'สามารถวิเคราะห์ปัญหาได้อย่างเป็นระบบ',
        },
      },
    },
  })
  @UseInterceptors(AnyFilesInterceptor())
  create(@Body() createCompetencyDto: CreateCompetenciesDto) {
    return this.competenciesService.create(createCompetencyDto);
  }

  //   @Get()
  //   @DefaultQueryParams()
  //   findAll(@Query() query: DataParams) {
  //     return this.competenciesService.findAll(query);
  //   }

  @Get()
  @DefaultQueryParams()
  @ApiQuery({
    name: 'comp_type',
    required: false,
    enum: ['สมรรถนะหลัก', 'ประจำสายงาน', 'ทางการบริหาร'],
    description: 'ประเภทของสมรรถนะ',
  })
  @ApiQuery({
    name: 'career_type',
    required: false,
    enum: ['วิชาการ', 'สนับสนุน'],
    description: 'สายงานของสมรรถนะ (ใช้ร่วมกับ comp_type = ประจำสายงาน)',
  })
  findAll(
    @Query() query: DataParams,
    @Query('comp_type') comp_type?: string,
    @Query('career_type') career_type?: string,
  ) {
    return this.competenciesService.findAll(query, comp_type, career_type);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.competenciesService.findOne(id);
  }

  @Patch(':id')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: 'object',
    schema: {
      type: 'object',
      properties: {
        comp_type: {
          type: 'string',
          enum: ['สมรรถนะหลัก', 'ประจำสายงาน', 'ทางการบริหาร'],
        },
        career_type: {
          type: 'string',
          enum: ['วิชาการ', 'สนับสนุน'],
        },
        name: { type: 'string' },
        description: { type: 'string' },
      },
    },
  })
  @UseInterceptors(AnyFilesInterceptor())
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCompetencyDto: UpdateCompetenciesDto,
  ) {
    return this.competenciesService.update(id, updateCompetencyDto);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.competenciesService.remove(id);
  }
}
