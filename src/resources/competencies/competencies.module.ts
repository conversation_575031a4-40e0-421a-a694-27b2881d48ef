import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CompetenciesService } from './competencies.service';
import { CompetenciesController } from './competencies.controller';
import { Competency } from './entities/competencies.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Competency])],
  controllers: [CompetenciesController],
  providers: [CompetenciesService],
  exports: [CompetenciesService, TypeOrmModule],
})
export class CompetenciesModule {}
