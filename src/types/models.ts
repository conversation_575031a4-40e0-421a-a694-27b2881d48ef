// ENUMS
export type RoleEnum =
  | 'Super Admin'
  | 'Administrator'
  | 'Manager'
  | 'Editor'
  | 'Standard User'
  | 'Guest'
  | 'kkp'
  | 'snh';
export type ItemBlockType = DropdownItemBlockType | 'HEADER' | 'IMAGE' | 'NEXTSECTION';
export type DropdownItemBlockType =
  | 'RADIO'
  | 'CHECKBOX'
  | 'TEXTFIELD'
  | 'GRID'
  | 'UPLOAD'
  | 'NEXTSECTION';

// USERS
export type User = {
  id: number;
  roleId: number;
  name: string;
  email: string;
  password?: string;
  currentPassword?: string;
  newPassword: string;
  dep_id?: number;

  roles?: Role[];
  dep?: DEP;
  psnPermissions?: { perId: number; perNameEn: string }[];
};

// ROLES
export type Role = {
  id: number;
  userId: number;
  name: RoleEnum;
  description: string;

  user?: User;
  permissions?: Permission[];
};

// PERMISSIONS
export type Permission = {
  id: number;
  roleId: number;
  name: string;
  nameEn: string;
  status: boolean;
  isDefault: boolean;
  role?: Role;
};

// PROGRAMS
export type Program = {
  id: number;
  creatorUserId: number;
  name: string;
  description: string;

  creatorUser?: User;
  assessments?: Assessment[];
};

// ASSESSMENTS
export type Assessment = {
  id: number;
  creatorUserId: number;
  programId: number;
  name: string;
  type: 'QUIZ' | 'EVALUATE';
  createdAt: string;
  startAt?: string;
  endAt?: string;
  submitLimit?: number;
  linkURL: string;
  responseEdit: boolean;
  status: boolean;
  totalScore: number;
  timeout: number;
  passRatio: number;
  program?: Program;
  creatorUser?: User;
  itemBlocks?: ItemBlock[];
  submissions?: Submission[];
  globalIsRequired?: boolean;
  isPrototype?: boolean;
};

// ITEM_BLOCKS
export type ItemBlock = {
  id: number;
  assessmentId: number;
  sequence: number;
  section: number;
  type: ItemBlockType;
  isRequired: boolean;

  headerBody?: HeaderBody;
  imageBody?: ImageBody;
  questions?: Question[];
  options?: Option[];
};

// HEADER_BODIES
export type HeaderBody = {
  id: number;
  itemBlockId: number;
  title: string;
  description?: string;
  nextSection?: number;
};

// IMAGE_BODIES
export type ImageBody = {
  id: number;
  itemBlockId: number;
  imageText?: string;
  imagePath?: string;
  imageWidth?: number;
  imageHeight?: number;
};

// QUESTIONS
export type Question = {
  id: number;
  itemBlockId: number;
  questionText: string;
  imagePath?: string;
  imageWidth?: number;
  imageHeight?: number;
  isHeader: boolean;
  sequence: number;
  sizeLimit?: number;
  acceptFile?: string;
  uploadLimit?: number;
  score: number;
};

// OPTIONS
export type Option = {
  id: number;
  itemBlockId: number;
  optionText: string;
  imagePath?: string;
  value: number;
  sequence: number;
  nextSection?: number;
};

// SUBMISSIONS
export type Submission = {
  id: number;
  assessmentId: number;
  userId: number;
  startAt: string;
  endAt: string;

  user?: User;
  responses?: Response[];
};

// RESPONSES
export type Response = {
  id: number;
  submissionId: number;
  selectedOptionId?: number;
  questionId?: number;
  filePath?: string;

  selectedOption?: Option;
  question?: Question;
};

export type getQuestionMeta = {
  questionBlock: ItemBlock;
  response: Response[] | null;
  isLast: boolean;
  questionList: questionList[];
};

export type questionList = {
  id: number;
  sequence: number;
  isDone: boolean;
};

export type Competency = {
  id: number;
  name: string;
  description?: string;
  type: string;
  career: string;
  skills?: Skill[];
};

export type Skill = {
  id: number;

  name: string;
  description?: string;
  dep_id: number;
  evaluatorId: number;
  competencyId: number;
  skill_type: string;
  career_type: string;
  programId: number;

  dep?: DEP;
  program?: Program;
  evaluator?: User;
  competency?: Competency;
};

export type DEP = {
  dep_id: number;
  name: string;
};

export type IDP = {
  id: number;
  name: string;
  creatorUserId?: number;
};

export type IDP_DEVELOPMENT_PLANS = {
  id: number;
  original_plan_id: number | null ;
  name: string;
  description: string;
  is_active: boolean;
};
