<template>
  <q-page padding>
    <div v-if="loading" class="text-center">
      <q-spinner size="lg" />
      <div class="q-mt-sm">กำลังโหลดข้อมูล...</div>
    </div>
    <div v-else-if="developmentPlan">
      <div class="text-h5 q-mb-md">{{ developmentPlan.name }}</div>
      <div class="text-body1">{{ developmentPlan.description }}</div>
      <!-- Add your development plan content here -->
    </div>
    <div v-else class="text-center text-grey-6">ไม่พบข้อมูลแผนพัฒนา</div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import type { IDP_DEVELOPMENT_PLANS } from 'src/types/models';
import { useGlobalStore } from 'src/stores/global';

const route = useRoute();
const globalStore = useGlobalStore();
const loading = ref(false);
const developmentPlan = ref<IDP_DEVELOPMENT_PLANS | null>(null);

// Mock data - same as in DevPlanManagement.vue
const mockCompetencies: IDP_DEVELOPMENT_PLANS[] = [
  {
    id: 1,
    original_plan_id: null,
    name: 'แผนพัฒนาศักยภาพผู้ปฏิบัติงานในมหาวิทยาลัย ปี 2567',
    description: 'ความสามารถในการวางแผน ดำเนินการ และควบคุมโครงการให้สำเร็จตามเป้าหมาย',
    is_active: true,
  },
  {
    id: 2,
    original_plan_id: null,
    name: 'แผนพัฒนาศักยภาพผู้ปฏิบัติงานในมหาวิทยาลัย ปี 2568',
    description: 'ทักษะในการสื่อสาร การนำเสนอข้อมูล และการประสานงานกับผู้อื่น',
    is_active: true,
  },
  {
    id: 3,
    original_plan_id: null,
    name: 'แผนพัฒนาศักยภาพผู้ปฏิบัติงานในมหาวิทยาลัย ปี 2569',
    description: 'ความสามารถในการวิเคราะห์ปัญหา ประมวลผลข้อมูล และหาแนวทางแก้ไข',
    is_active: true,
  },
  {
    id: 4,
    original_plan_id: null,
    name: 'แผนพัฒนาศักยภาพผู้ปฏิบัติงานในมหาวิทยาลัย ปี 2570',
    description: 'ความสามารถในการเป็นผู้นำ การมอบหมายงาน และการสร้างแรงบันดาลใจให้ทีม',
    is_active: false,
  },
  {
    id: 5,
    original_plan_id: null,
    name: 'แผนพัฒนาศักยภาพผู้ปฏิบัติงานในมหาวิทยาลัย ปี 2571',
    description: 'ทักษะการใช้เทคโนโลยี การใช้งานซอฟต์แวร์ และการปรับตัวกับเทคโนโลยีใหม่',
    is_active: false,
  },
];

// Function to extract year from plan name
const extractYearFromPlanName = (planName: string): string => {
  const yearMatch = planName.match(/ปี\s*(\d{4})/);
  return yearMatch?.[1] ?? 'XXXX';
};

// Function to update global store with dynamic breadcrumb
const updateDevelopmentPlanBreadcrumb = (plan: IDP_DEVELOPMENT_PLANS | null) => {
  if (plan) {
    const year = extractYearFromPlanName(plan.name);
    globalStore.setDevelopmentPlanYear(year);
  }
};

// Function to fetch development plan by ID
const fetchDevelopmentPlan = (id: number) => {
  loading.value = true;
  try {
    // In a real application, this would be an API call
    // For now, using mock data
    const plan = mockCompetencies.find((p) => p.id === id);
    developmentPlan.value = plan || null;
    updateDevelopmentPlanBreadcrumb(plan || null);
  } catch (error) {
    console.error('Error fetching development plan:', error);
    developmentPlan.value = null;
    updateDevelopmentPlanBreadcrumb(null);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  const planId = Number(route.params.id);
  if (planId && !isNaN(planId)) {
    fetchDevelopmentPlan(planId);
  }
});

// Watch for route parameter changes
watch(
  () => route.params.id,
  (newId) => {
    const planId = Number(newId);
    if (planId && !isNaN(planId)) {
      fetchDevelopmentPlan(planId);
    }
  },
);

// Clear breadcrumb when component unmounts
onUnmounted(() => {
  globalStore.clearDevelopmentPlanYear();
});
</script>

<style scoped></style>
