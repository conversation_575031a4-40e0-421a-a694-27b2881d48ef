<template>
  <q-page padding class="q-gutter-y-lg">
    <TopHeaderTable
      title="จัดการความรู้และทักษะ"
      create-button-label="เพิ่ม"
      @search="onSearchUpdate"
      @create="onClickAdd"
    />

    <TabNavigation class="q-mt-sm" :tabs="tabItems" v-model="selectedTab" />

    <q-table
      :rows="rows"
      :columns="skillManagementColumns"
      row-key="id"
      flat
      bordered
      wrap-cells
      separator="cell"
    >
      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <div class="q-gutter-x-sm flex justify-center">
            <q-btn
              dense
              unelevated
              class="view-icon"
              icon="visibility"
              :disable="isDisable"
              @click="onClickDelete(row)"
            />

            <q-btn
              dense
              unelevated
              class="view-icon"
              icon="edit"
              :disable="userRole == 'kkp' && userDpId !== row.dep_id"
              @click="onClickEdit(row)"
              style="background-color: #303f9f"
              :class="{ 'disabled-btn-gray': userRole == 'kkp' && userDpId !== row.dep_id }"
            >
              <q-tooltip
                v-if="userRole == 'kkp' && userDpId !== row.dep_id"
                class="bg-red text-white"
                :offset="[10, 10]"
              >
                ไม่สามารถแก้ไขได้
              </q-tooltip>
            </q-btn>

            <q-btn
              dense
              unelevated
              class="view-icon"
              icon="delete"
              :disable="userRole == 'kkp' && userDpId !== row.dep_id"
              @click="onClickDelete(row)"
              style="background-color: #ab2433"
              :class="{ 'disabled-btn-gray': userRole == 'kkp' && userDpId !== row.dep_id }"
            >
              <q-tooltip
                v-if="userRole == 'kkp' && userDpId !== row.dep_id"
                class="bg-red text-white"
                :offset="[10, 10]"
              >
                ไม่สามารถลบได้
              </q-tooltip>
            </q-btn>
          </div>
        </q-td>
      </template>
    </q-table>
  </q-page>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { defineAsyncComponent } from 'vue';
import { skillManagementColumns } from 'src/data/table_columns';
import type { User } from 'src/types/models';
import { type Skill } from 'src/types/models';
import TopHeaderTable from 'src/components/common/TopHeaderTable.vue';
import TabNavigation from 'src/components/common/TabNavigation.vue';
import { useSkillStore } from 'src/stores/skills';
import { useAuthStore } from 'src/stores/auth';

const $q = useQuasar();
const skillStore = useSkillStore();
const authStore = useAuthStore();
const user = ref<User>();
const isDisable = ref(false);

const mockUserKkp: User = {
  id: 101,
  roleId: 2, // This is okay if 2 maps to 'kkp' in your system
  name: 'สมชาย รักชาติ',
  email: '<EMAIL>',
  newPassword: '',
  roles: [
    { id: 1, name: 'Super Admin', description: 'ผู้ดูแลระบบ', userId: 101 },
    { id: 2, name: 'kkp', description: 'ผู้ใช้งาน KKP', userId: 101 }, // Added 'kkp' role
  ],
  psnPermissions: [
    { perId: 201, perNameEn: 'View Reports' },
    { perId: 202, perNameEn: 'Manage Data' },
  ],
  dep_id: 0,
};

const hasKkpRole = mockUserKkp.roles?.some((role) => role.name === 'kkp') || false;
const userRole = hasKkpRole ? 'kkp' : mockUserKkp.roles?.[0]?.name || '';
console.log(userRole);
const userDpId = mockUserKkp.dep_id;
console.log(userDpId);
onMounted(() => {
  user.value = authStore.getCurrentUser();
});

const tabItems = [
  { label: 'ความรู้และทักษะทั่วไปของบุคลากร', value: 'general-staff' },
  { label: 'ความรู้และทักษะทั่วไปของผู้บริหาร', value: 'general-admin' },
  { label: 'ความรู้และทักษะเฉพาะด้านของสายวิชาการ', value: 'academic' },
  { label: 'ความรู้และทักษะเฉพาะด้านของสายสนับสนุนวิชาการ', value: 'support' },
  { label: 'เฉพาะด้านของผู้บริหาร', value: 'admin' },
];

const TAB_KEY = 'selected-skill-tab';
const selectedTab = ref(localStorage.getItem(TAB_KEY) || 'general-staff');

// เก็บค่าไว้ทุกครั้งที่เปลี่ยน
watch(selectedTab, (val) => {
  localStorage.setItem(TAB_KEY, val);
});

const filteredRowsByTab = ref<Skill[]>([]);
const searchKeyword = ref('');

// Function to filter skills by tab
const filterSkillsByTab = (tabValue: string) => {
  const allSkills = skillStore.skills;
  console.log(allSkills, 'eiei');
  switch (tabValue) {
    case 'general-staff':
      filteredRowsByTab.value = allSkills.filter(
        (item) => item.career_type === 'Staff' || item.career_type === 'บุคลากร',
      );
      break;
    case 'general-admin':
      filteredRowsByTab.value = allSkills.filter(
        (item) => item.career_type === 'Admin' || item.career_type === 'ผู้บริหาร',
      );
      break;
    case 'academic':
      filteredRowsByTab.value = allSkills.filter(
        (item) => item.career_type === 'Academic' || item.career_type === 'สายวิชาการ',
      );
      break;
    case 'support':
      filteredRowsByTab.value = allSkills.filter(
        (item) => item.career_type === 'Support' || item.career_type === 'สายสนับสนุนวิชาการ',
      );
      break;
    case 'admin':
      filteredRowsByTab.value = allSkills.filter(
        (item) => item.career_type === 'Admin' || item.career_type === 'ผู้บริหาร',
      );
      break;
    default:
      filteredRowsByTab.value = allSkills;
  }
};

// Watcher to filter rows based on selected tab
watch(selectedTab, (newVal) => {
  filterSkillsByTab(newVal);
});

// Initial filtering when component is mounted
onMounted(() => {
  filterSkillsByTab(selectedTab.value);
});

// Computed property for rows displayed in the table (filtered by search keyword)
const rows = computed(() => {
  if (!searchKeyword.value) {
    return filteredRowsByTab.value;
  } else {
    const keyword = searchKeyword.value.toLowerCase();
    return filteredRowsByTab.value.filter(
      (item) =>
        item.name?.toLowerCase().includes(keyword) ||
        item.description?.toLowerCase().includes(keyword) ||
        item.skill_type?.toLowerCase().includes(keyword) ||
        item.career_type?.toLowerCase().includes(keyword),
    );
  }
});

const onClickEdit = (row: Skill) => {
  $q.dialog({
    component: defineAsyncComponent(() => import('src/components/skill/createSkillDialog.vue')),
    componentProps: {
      title: 'แก้ไขความรู้และทักษะ',
      formData: row,
      skillType: selectedTab,
    },
    persistent: true,
  })
    .onOk((data: Skill) => {
      console.log('Updated skill data:', data);
      skillStore.editSkill(data);
    })
    .onCancel(() => {
      console.log('Edit dialog cancelled');
    })
    .onDismiss(() => {
      console.log('Edit dialog dismissed');
    });
};

const onClickDelete = (row: Skill) => {
  console.log('Delete row:', row);
  $q.dialog({
    title: 'ยืนยันการลบ',
    message: `คุณต้องการลบความรู้และทักษะ "${row.name}" ใช่หรือไม่?`,
    cancel: true,
    persistent: true,
  })
    .onOk(() => {
      //skillStore.deleteSkill(row.id);
      console.log('Skill deleted:', row.id);
    })
    .onCancel(() => {
      console.log('Delete cancelled');
    });
};

const onClickAdd = () => {
  $q.dialog({
    component: defineAsyncComponent(() => import('src/components/skill/createSkillDialog.vue')),
    componentProps: {
      title: 'สร้างความรู้และทักษะใหม่',
    },
    persistent: true,
  })
    .onOk((data: Skill) => {
      console.log('New skill data:', data);
      skillStore.addSkill(data);
    })
    .onCancel(() => {
      console.log('Add dialog cancelled');
    })
    .onDismiss(() => {
      console.log('Add dialog dismissed');
    });
};

const onSearchUpdate = (keyword: string) => {
  searchKeyword.value = keyword;
};

// Watch skillStore.skills to re-filter when the store data changes
watch(
  () => skillStore.skills,
  () => {
    filterSkillsByTab(selectedTab.value);
  },
);
</script>
<style scoped>
.disabled-btn-gray {
  background-color: #a0a0a0 !important;
}

.disabled-btn-gray .q-icon {
  opacity: 0.6;
}
</style>
