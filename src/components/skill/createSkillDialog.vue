<template>
  <q-dialog ref="dialogRef" persistent>
    <q-card class="container" style="max-width: 900px">
      <q-card-section>
        <div class="text-h6">{{ title }}</div>
      </q-card-section>
      <q-form ref="formRef" @submit.prevent="submitForm">
        <q-card-section class="q-pt-none">
          <div class="q-gutter-y-md">
            <q-card-section style="border: 0.5px solid black; border-radius: 15px; height: 110px">
              <div class="text-subtitle1 q-mb-sm">ชื่อความรู้และทักษะ</div>

              <q-input
                v-model="formDataRef.name"
                :rules="[(val) => !!val || 'กรุณากรอกชื่อความรู้และทักษะ']"
                outlined
                dense
              />
            </q-card-section>
            <q-card-section style="border: 0.5px solid black; border-radius: 15px">
              <div class="text-subtitle1 q-mb-sm">ประเภทความรู้และทักษะ</div>

              <div class="row q-gutter-sm">
                <div class="col-xs-12 col-sm-6">
                  <q-select
                    v-model="formDataRef.skill_type"
                    label="ประเภทความรู้และทักษะ"
                    :options="skillTypeOptions"
                    outlined
                    dense
                    class="full-width"
                  />
                </div>
                <div class="col-xs-12 col-sm-5">
                  <q-select
                    v-model="formDataRef.skill_type"
                    label="ประเภทสายงาน"
                    :options="skillTypeOptions"
                    outlined
                    dense
                    class="full-width"
                  />
                </div>
              </div>
            </q-card-section>

            <q-card-section
              style="border: 0.5px solid black; border-radius: 15px; max-height: 150px"
            >
              <div class="text-subtitle1 q-mb-sm">ต้องการติดตามหรือไม่</div>
              <div class="q-gutter-sm">
                <q-radio v-model="tacking" val="want" label="ต้องการติดตาม" color="primary" />
                <q-radio
                  v-model="tacking"
                  val="not_want"
                  label="ไม่ต้องการติดตาม"
                  color="primary"
                />
              </div>
            </q-card-section>

            <q-card-section style="border: 0.5px solid black; border-radius: 15px; height: 215px">
              <div class="text-subtitle1 q-mb-sm">คำอธิบายเพิ่มเติม</div>

              <q-input
                v-model="formDataRef.description"
                label="คำอธิบายเพิ่มเติม"
                type="textarea"
                outlined
              />
            </q-card-section>
            <q-card-section style="border: 0.5px solid black; border-radius: 15px">
              <div class="text-subtitle1 q-mb-sm" style="width: 100%">
                สมรรถนะที่เชื่อมโยง
                <q-btn
                  icon="add"
                  label="เพิ่มสมรรถนะใหม่"
                  style="background-color: #4050b5; color: white; margin-left: 520px"
                  @click="onAddCompetency"
                />
              </div>

              <div class="row q-gutter-sm">
                <div class="text-subtitle1 q-mb-sm" style="margin-top: 13px">ค้นหาโดย:</div>
                <q-select
                  v-model="formDataRef.skill_type"
                  label="สมรรถณะหลัก"
                  :options="skillTypeOptions"
                  outlined
                  dense
                  style="width: 50%"
                /><q-select
                  v-model="formDataRef.skill_type"
                  label="ประเภทสายงาน"
                  :options="skillTypeOptions"
                  outlined
                  dense
                  style="width: 39%"
                />
              </div>
              <div class="q-mt-sm">
                <q-select
                  v-model="selectedSamata"
                  label="ค้นหาสมรรถนะหลัก"
                  :options="skillTypeOptions"
                  outlined
                  dense
                  style="width: 100%"
                >
                  <template #prepend>
                    <q-icon name="search" />
                  </template>
                </q-select>
              </div>
            </q-card-section>
          </div>
        </q-card-section>

        <q-card-actions align="right" class="q-pa-md">
          <div class="q-gutter-sm">
            <q-btn
              icon="close"
              label="ยกเลิก"
              flat
              color="grey-7"
              @click="onDialogCancel"
              style="border: 1px solid rgba(0, 0, 0, 0.3)"
            />
            <q-btn icon="check" label="ยืนยัน" color="positive" @click="submitForm" />
          </div>
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { QForm, useDialogPluginComponent } from 'quasar';
import type { Skill } from 'src/types/models';

const tacking = ref('');
const selectedSamata = ref('');
const props = defineProps<{
  title: string;
  formData?: Skill;
  skillType: string;
}>();

// Properly destructure the dialog plugin component
const { dialogRef, onDialogOK, onDialogCancel } = useDialogPluginComponent();

const onAddCompetency = () => {
  console.log('in');
};
const formRef = ref<QForm | null>(null);
const formDataRef = ref<Skill>({
  id: 0,
  competencyId: 0,
  name: '',
  description: '',
  skill_type: '',
  career_type: '',
  dep_id: 0,
  evaluatorId: 0,
  programId: 0,
});

// Options for dropdowns
const skillTypeOptions = [
  'Technical Skill',
  'Communication Skill',
  'Management Skill',
  'Analytical Skill',
  'Problem Solving Skill',
  'Leadership Skill',
  'Creative Skill',
  'Interpersonal Skill',
];

onMounted(() => {
  if (props.formData) {
    formDataRef.value = { ...props.formData };
  } else {
    formDataRef.value = {
      id: 0,
      competencyId: 0,
      name: '',
      description: '',
      skill_type: '',
      career_type: '',
      programId: 0,
      evaluatorId: 0,
      dep_id: 0,
    };
  }
});

const submitForm = async () => {
  if (formRef.value) {
    try {
      const isValid = await formRef.value.validate();
      if (isValid) {
        onDialogOK(formDataRef.value);
      }
    } catch (error) {
      console.error('Form validation error:', error);
    }
  }
};
</script>

<style scoped></style>
