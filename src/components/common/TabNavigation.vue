<script setup lang="ts">
import { ref, watch } from 'vue';

interface TabItem {
  label: string;
  value: string;
}

const props = defineProps<{
  tabs: TabItem[];
  modelValue: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', val: string): void;
}>();

const tab = ref(props.modelValue);

watch(tab, (val) => emit('update:modelValue', val));
</script>

<template>
  <q-tabs
    v-model="tab"
    dense
    class="text-grey-8 bg-white"
    active-color="primary"
    indicator-color="primary"
    align="left"
    narrow-indicator
  >
    <q-tab
      v-for="item in tabs"
      :key="item.value"
      :name="item.value"
      :label="item.label"
      class="q-px-md"
    />
  </q-tabs>
</template>
