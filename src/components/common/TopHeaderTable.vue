<!-- <template>
  <div class="top-header-table">
    Title
    <div class="text-h6 q-mb-md">{{ title }}</div>

    Top toolbar: Optional Search + Optional Create button 
    <div class="row items-center q-gutter-sm justify-end q-mb-md">
      <SearchBar v-if="showSearch" @search="handleSearch" />
      <q-btn
        v-if="showCreateButton"
        :label="createButtonLabel"
        color="accent"
        icon="add"
        @click="handleCreate"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import SearchBar from 'src/components/SearchBar.vue';

interface Props {
  title: string;
  createButtonLabel?: string;
  showCreateButton?: boolean;
  showSearch?: boolean;
}

interface Emits {
  (e: 'search', keyword: string): void;
  (e: 'create'): void;
}

withDefaults(defineProps<Props>(), {
  createButtonLabel: 'สร้าง',
  showCreateButton: true,
  showSearch: true,
});

const emit = defineEmits<Emits>();

const handleSearch = (keyword: string) => {
  emit('search', keyword);
};

const handleCreate = () => {
  emit('create');
};
</script>

<style scoped lang="scss">
.top-header-table {
  margin-bottom: 0;
}
</style> -->

<template>
  <div class="top-header-table">
    <!-- แถวหลัก แบ่งซ้าย-ขวา -->
    <div class="row q-mt-sm q-mb-sm items-center justify-between">
      <!-- ✅ ฝั่งซ้าย: Title + Tab + Subtitle -->
      <div class="col-auto">
        <div class="text-h6">{{ title }}</div>
        <slot name="tab" />
        <div v-if="subtitle" class="text-subtitle1 text-weight-medium q-mt-lg">
          {{ subtitle }}
        </div>
      </div>

      <!-- ✅ ฝั่งขวา: Search + ปุ่มเพิ่ม -->
      <div class="items-center q-gutter-sm">
        <div class="q-mt-sm flex justify-end">
          <q-btn
            v-if="showCreateButton"
            :label="createButtonLabel"
            color="accent"
            icon="add"
            @click="handleCreate"
          />
        </div>
        <div class="row items-center justify-between">
          <!-- ซ้าย: SearchBar -->
          <div class="col">
            <SearchBar v-if="showSearch" @search="handleSearch" />
          </div>

          <!-- ขวา: ปุ่มกรอง -->
          <div class="col-auto">
            <q-btn round dense flat>
              <q-icon name="filter_list" />
            </q-btn>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SearchBar from 'src/components/SearchBar.vue';

interface Props {
  title: string;
  createButtonLabel?: string;
  showCreateButton?: boolean;
  showSearch?: boolean;
  subtitle?: string;
}

interface Emits {
  (e: 'search', keyword: string): void;
  (e: 'create'): void;
}

withDefaults(defineProps<Props>(), {
  createButtonLabel: 'สร้าง',
  showCreateButton: true,
  showSearch: true,
});

const emit = defineEmits<Emits>();

const handleSearch = (keyword: string) => {
  emit('search', keyword);
};

const handleCreate = () => {
  emit('create');
};
</script>
