import { api as axios } from 'src/boot/axios';
import { Notify } from 'quasar';
import type { Skill } from 'src/types/models';

const showError = (message: string) => {
  Notify.create({
    type: 'negative',
    message,
    position: 'bottom',
    timeout: 3000,
  });
};

export class SkillsService {
  private path = '/skills';

  async fecthAll(): Promise<Skill[]> {
    try {
      const res = await axios.get<Skill[]>(this.path);
      return res.data;
    } catch {
      showError('ดึงข้อมูลทักษะทั้งหมดล้มเหลว');
      throw new Error('Get all Skills failed');
    }
  }

  async fecthOne(id: number): Promise<Skill> {
    try {
      const res = await axios.get<Skill>(`${this.path}/${id}`);
      return res.data;
    } catch {
      showError('ดึงข้อมูลทักษะล้มเหลว');
      throw new Error('Get all Skills failed');
    }
  }

  async create(data: Skill): Promise<Skill> {
    try {
      const payload = {
        ...data,
        endAt: '',
      };
      const res = await axios.post<Skill>(this.path, payload);
      return res.data;
    } catch {
      showError('สร้างทักษะไม่สำเร็จ');
      throw new Error('Create summary failed');
    }
  }

  async update(skillId: number) {
    try {
      const res = await axios.patch<Skill>(`${this.path}/skills/${skillId}`);
      return res.data;
    } catch {
      showError('อัปเดตทักษะล้มเหลว');
      throw new Error('Update summary failed');
    }
  }

  async remove(id: number): Promise<void> {
    try {
      await axios.delete(`${this.path}/${id}`);
    } catch {
      showError('ลบทักษะล้มเหลว');
      throw new Error('Remove skill failed');
    }
  }
}

export const summaryService = new SkillsService();
