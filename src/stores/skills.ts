import { defineStore } from 'pinia';
import type { Skill } from 'src/types/models';
import { ref } from 'vue';
//import apiService from '../services/apiService';

export const useSkillStore = defineStore('skill', () => {
  const skills: Skill[] = [
    {
      id: 1,
      competencyId: 1,
      name: 'การวางแผนโครงการ',
      description: 'ความสามารถในการวางแผนขั้นตอนการดำเนินงานโครงการ',
      skill_type: 'Technical Skill',
      career_type: 'บุคลากร',
      dep_id: 0,
      evaluatorId: 0,
      programId: 0,
    },
    {
      id: 2,
      competencyId: 1,
      name: 'การติดตามผลการดำเนินงาน',
      description: 'ทักษะในการควบคุมและติดตามความคืบหน้าของโครงการ',
      skill_type: 'Management Skill',
      career_type: 'บุคลากร',
      dep_id: 1,
      evaluatorId: 0,
      programId: 0,
    },
    {
      id: 3,
      competencyId: 2,
      name: 'การนำเสนอต่อผู้บริหาร',
      description: 'ความสามารถในการนำเสนอข้อมูลและผลงานต่อผู้บริหารอย่างมีประสิทธิภาพ',
      skill_type: 'Communication Skill',
      career_type: 'ผู้บริหาร',
      dep_id: 1,
      evaluatorId: 0,
      programId: 0,
    },
    {
      id: 4,
      competencyId: 2,
      name: 'การเขียนรายงาน',
      description: 'ทักษะในการเขียนรายงานที่ชัดเจนและเข้าใจง่าย',
      skill_type: 'Communication Skill',
      career_type: 'บุคลากร',
      dep_id: 0,
      evaluatorId: 0,
      programId: 0,
    },
    {
      id: 5,
      competencyId: 3,
      name: 'การวิเคราะห์ข้อมูล',
      description: 'ความสามารถในการประมวลผลและวิเคราะห์ข้อมูลเชิงตัวเลข',
      skill_type: 'Analytical Skill',
      career_type: 'สายวิชาการ',
      dep_id: 1,
      evaluatorId: 0,
      programId: 0,
    },
    {
      id: 6,
      competencyId: 3,
      name: 'การแก้ไขปัญหาเชิงระบบ',
      description: 'ทักษะในการหาสาเหตุและแนวทางแก้ไขปัญหาอย่างเป็นระบบ',
      skill_type: 'Problem Solving Skill',
      career_type: 'สายสนับสนุนวิชาการ',
      dep_id: 0,
      evaluatorId: 0,
      programId: 0,
    },
    {
      id: 7,
      competencyId: 4,
      name: 'การสร้างแรงบันดาลใจ',
      description: 'ความสามารถในการสร้างแรงจูงใจและกระตุ้นทีมงาน',
      skill_type: 'Leadership Skill',
      career_type: 'ผู้บริหาร',
      dep_id: 1,
      evaluatorId: 0,
      programId: 0,
    },
    {
      id: 8,
      competencyId: 5,
      name: 'การใช้งาน Microsoft Office',
      description: 'ทักษะในการใช้โปรแกรม Word, Excel, PowerPoint อย่างชำนาญ',
      skill_type: 'Technical Skill',
      career_type: 'บุคลากร',
      dep_id: 0,
      evaluatorId: 0,
      programId: 0,
    },
  ];
  //const skills = ref<Skill>([])
  const skill = ref<Skill>({
    id: 0,
    name: '',
    dep_id: 0,
    evaluatorId: 0,
    competencyId: 0,
    skill_type: '',
    career_type: '',
    programId: 0,
  });
  const editedSkill = skill;

  function fetchSkills() {
    console.log('Fetching skills from backend...');
    // TODO: connect to backend API
  }

  function addSkill(skill: Skill) {
    const newId = Math.max(0, ...skills.map((r) => r.id)) + 1;
    skills.push({ ...skill, id: newId });
  }

  function editSkill(updatedSkill: Skill) {
    const index = skills.findIndex((item) => item.id === updatedSkill.id);
    if (index !== -1) {
      skills[index] = { ...updatedSkill };
    }
  }

  // function deleteSkill(skillId: number) {
  //   skills = skills.filter((item) => item.id !== skillId);
  // }
  return {
    //state
    skills,
    editedSkill,

    //action
    fetchSkills,
    addSkill,
    editSkill,
    //deleteSkill,
  };
});
